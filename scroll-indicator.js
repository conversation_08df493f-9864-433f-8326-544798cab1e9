// Scroll Indicator Functionality
class ScrollIndicator {
    constructor() {
        this.scrollIndicator = document.getElementById('scroll-indicator');
        this.init();
    }

    init() {
        if (!this.scrollIndicator) return;
        
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Click event for scroll indicator
        this.scrollIndicator.addEventListener('click', () => {
            this.scrollToNextSection();
        });

        // Optional: Hide scroll indicator when user scrolls down
        window.addEventListener('scroll', () => {
            this.handleScroll();
        });
    }

    scrollToNextSection() {
        // Scroll to the music preview section (next section after hero)
        const musicSection = document.querySelector('.music-preview-section');
        if (musicSection) {
            musicSection.scrollIntoView({ 
                behavior: 'smooth',
                block: 'start'
            });
        } else {
            // Fallback: scroll down by viewport height
            window.scrollBy({
                top: window.innerHeight,
                behavior: 'smooth'
            });
        }
    }

    handleScroll() {
        // Hide scroll indicator when user has scrolled down
        const scrollPosition = window.scrollY;
        const viewportHeight = window.innerHeight;
        
        if (scrollPosition > viewportHeight * 0.1) {
            this.scrollIndicator.style.opacity = '0';
            this.scrollIndicator.style.pointerEvents = 'none';
        } else {
            this.scrollIndicator.style.opacity = '1';
            this.scrollIndicator.style.pointerEvents = 'auto';
        }
    }
}

// Initialize scroll indicator when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ScrollIndicator();
});
